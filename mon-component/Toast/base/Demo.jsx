import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Toast } from '@roo/roo-b-mobile' */
import { Toast, Button, Icon } from '@roo/roo-b-mobile';
import '../index.scss';

function Demo() {
    return (
        <div className="demo-container">
            <h5>基础用法</h5>
            <br />
            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '纯文字Toast',
                    });
                }}
            >
                文字提示
            </Button>
            <br />
            <br />

            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '纯文字Toast',
                        mask: true,
                    });
                }}
            >
                文字提示，带遮罩层
            </Button>
            <br />
            <br />

            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '纯文字Toast',
                        mask: true,
                        maskClickable: true,
                    });
                }}
            >
                文字提示，带遮罩层，遮罩层可点击
            </Button>
            <br />
            <br />
            <h5>内置状态及自定义icon</h5>
            <br />
            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '这是Toast',
                        icon: 'loading',
                    });
                }}
            >
                加载提示
            </Button>
            <br />
            <br />

            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '这是一个成功的Toast提示消息，现在宽度已经增大了',
                        icon: 'success',
                        duration: 0,
                        style: {
                            width: '280px',
                            maxWidth: '320px',
                        },
                        className: 'custom',
                    });
                }}
            >
                成功提示
            </Button>
            <br />
            <br />

            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '这是Toast',
                        icon: 'fail',
                    });
                }}
            >
                失败提示
            </Button>
            <br />
            <br />

            <Button
                type="button"
                onClick={() => {
                    Toast.open({
                        content: '自定义icon',
                        maskClickable: true,
                        icon: (
                            <div>
                                <Icon name="search" size={20} />
                            </div>
                        ),
                        maskStyle: {
                            background: 'rgba(0,0,0,0.1)',
                        },
                    });
                }}
            >
                自定义图标
            </Button>
        </div>
    );
}
export default Demo;
